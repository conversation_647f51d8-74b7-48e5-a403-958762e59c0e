import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 数据库表结构类型定义
export interface MusicTrack {
  id: string
  title: string
  artist: string
  duration: string
  audio_url: string
  cover_image?: string
  description?: string
  created_at: string
  updated_at: string
}

export interface VideoItem {
  id: string
  title: string
  description?: string
  bilibili_url: string
  bilibili_embed_id: string
  thumbnail_url?: string
  created_at: string
  updated_at: string
}

export interface ProfileInfo {
  id: string
  name: string
  bio: string
  avatar_url?: string
  social_links?: {
    bilibili?: string
    weibo?: string
    email?: string
  }
  updated_at: string
}
