import Image from "next/image";
import Link from "next/link";
import { Play, Music, Film } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/electric-guitar-bg.jpg"
            alt="Electric Guitar Background"
            fill
            className="object-cover object-center"
            priority
          />
          <div className="absolute inset-0 bg-black/70"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-6xl md:text-8xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent font-serif italic tracking-wider">
            KIMAHALA
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed tracking-wide">
            MUSICIAN · CONTENT CREATOR
          </p>
          <p className="text-lg text-gray-400 mb-12 max-w-2xl mx-auto">
            Expressing emotions through music, sharing cinematic experiences.
            Welcome to my world of original music and movie recommendations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/music"
              className="inline-flex items-center px-8 py-4 bg-red-600 text-white font-semibold tracking-wider hover:bg-red-700 transition-colors duration-200"
            >
              <Music className="mr-2" size={20} />
              EXPLORE MUSIC
            </Link>
            <Link
              href="/video"
              className="inline-flex items-center px-8 py-4 border-2 border-red-600 text-red-600 font-semibold tracking-wider hover:bg-red-600 hover:text-white transition-colors duration-200"
            >
              <Video className="mr-2" size={20} />
              WATCH VIDEOS
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Content */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-center mb-16 tracking-wider">FEATURED WORKS</h2>

          <div className="grid md:grid-cols-2 gap-12">
            {/* Latest Music */}
            <div className="bg-gray-900 rounded-lg p-8 hover:bg-gray-700 transition-colors duration-200">
              <div className="flex items-center mb-6">
                <Music className="mr-3 text-red-400" size={24} />
                <h3 className="text-2xl font-semibold tracking-wide">LATEST MUSIC</h3>
              </div>
              <p className="text-gray-400 mb-6">
                Listen to my latest musical creations and feel the power of emotions expressed through sound.
              </p>
              <Link
                href="/music"
                className="inline-flex items-center text-red-400 hover:text-red-300 font-medium tracking-wide"
              >
                <Play className="mr-2" size={16} />
                PLAY NOW
              </Link>
            </div>

            {/* Latest Video */}
            <div className="bg-gray-900 rounded-lg p-8 hover:bg-gray-700 transition-colors duration-200">
              <div className="flex items-center mb-6">
                <Video className="mr-3 text-red-400" size={24} />
                <h3 className="text-2xl font-semibold tracking-wide">LATEST VIDEOS</h3>
              </div>
              <p className="text-gray-400 mb-6">
                Watch my latest video content on Bilibili, including music creation process and life sharing.
              </p>
              <Link
                href="/video"
                className="inline-flex items-center text-red-400 hover:text-red-300 font-medium tracking-wide"
              >
                <Play className="mr-2" size={16} />
                WATCH NOW
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
