'use client'

import { useState, useEffect } from 'react'
import { Play, Pause, Music, Clock } from 'lucide-react'
import ReactAudioPlayer from 'react-audio-player'

export default function MusicPage() {
  const [musicTracks, setMusicTracks] = useState<any[]>([])
  const [currentTrack, setCurrentTrack] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchMusicTracks()
  }, [])

  const fetchMusicTracks = async () => {
    try {
      const response = await fetch('/api/music')
      if (response.ok) {
        const data = await response.json()
        setMusicTracks(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch music tracks:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePlayPause = (trackId: string) => {
    if (currentTrack === trackId) {
      setIsPlaying(!isPlaying)
    } else {
      setCurrentTrack(trackId)
      setIsPlaying(true)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen py-12 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading music...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <Music className="text-red-400" size={48} />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wider">MUSIC</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Here you'll find my original musical compositions, each song carrying different emotions and stories
          </p>
        </div>

        {/* Music Grid */}
        <div className="grid gap-8">
          {musicTracks.map((track) => (
            <div
              key={track.id}
              className="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors duration-200"
            >
              <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                {/* Cover Image */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 bg-gray-600 rounded-lg flex items-center justify-center">
                    <Music className="text-gray-400" size={32} />
                  </div>
                </div>

                {/* Track Info */}
                <div className="flex-grow">
                  <h3 className="text-2xl font-semibold mb-2">{track.title}</h3>
                  <p className="text-gray-400 mb-2">演唱：{track.artist}</p>
                  <p className="text-gray-500 text-sm mb-4">{track.description}</p>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="mr-1" size={16} />
                    <span className="mr-4">{track.duration}</span>
                    <span>发布于 {track.created_at}</span>
                  </div>
                </div>

                {/* Play Button */}
                <div className="flex-shrink-0">
                  <button
                    onClick={() => handlePlayPause(track.id)}
                    className="w-16 h-16 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors duration-200"
                  >
                    {currentTrack === track.id && isPlaying ? (
                      <Pause className="text-white" size={24} />
                    ) : (
                      <Play className="text-white ml-1" size={24} />
                    )}
                  </button>
                </div>
              </div>

              {/* Audio Player */}
              {currentTrack === track.id && (
                <div className="mt-6 pt-6 border-t border-gray-600">
                  <div className="audio-player">
                    <ReactAudioPlayer
                      src={track.audio_url}
                      autoPlay={isPlaying}
                      controls
                      className="w-full"
                      onPlay={() => setIsPlaying(true)}
                      onPause={() => setIsPlaying(false)}
                      onEnded={() => setIsPlaying(false)}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Empty State */}
        {musicTracks.length === 0 && (
          <div className="text-center py-20">
            <Music className="mx-auto text-gray-600 mb-4" size={64} />
            <h3 className="text-2xl font-semibold text-gray-400 mb-2">
              No Music Available
            </h3>
            <p className="text-gray-500">
              Stay tuned for more amazing musical works
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
