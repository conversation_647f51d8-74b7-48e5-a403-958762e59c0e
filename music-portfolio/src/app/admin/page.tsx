'use client'

import { useState, useEffect } from 'react'
import { Settings, Music, Video, User, Plus, Edit, Trash2, Film } from 'lucide-react'

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('music')
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查认证状态
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/admin/auth', {
        credentials: 'include'
      })
      setIsAuthenticated(response.ok)
    } catch (error) {
      setIsAuthenticated(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogin = () => {
    const username = prompt('请输入用户名:')
    const password = prompt('请输入密码:')

    if (username && password) {
      const credentials = btoa(`${username}:${password}`)
      fetch('/api/admin/auth', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      }).then(response => {
        if (response.ok) {
          // 保存认证信息到localStorage
          localStorage.setItem('admin_username', username)
          localStorage.setItem('admin_password', password)
          setIsAuthenticated(true)
        } else {
          alert('认证失败，请检查用户名和密码')
        }
      })
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-400">加载中...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg max-w-md w-full mx-4">
          <div className="text-center mb-8">
            <Settings className="mx-auto text-blue-400 mb-4" size={48} />
            <h1 className="text-2xl font-bold mb-2">后台管理系统</h1>
            <p className="text-gray-400">请登录以访问管理功能</p>
          </div>
          
          <button
            onClick={handleLogin}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            登录
          </button>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'music', label: '音乐管理', icon: Music },
    { id: 'movie', label: '电影管理', icon: Film },
    { id: 'video', label: '视频管理', icon: Video },
    { id: 'profile', label: '个人信息', icon: User },
  ]

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">后台管理系统</h1>
          <p className="text-gray-400">管理您的音乐和视频内容</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-700 mb-8">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="mr-2" size={20} />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="bg-gray-800 rounded-lg p-6">
          {activeTab === 'music' && <MusicManagement />}
          {activeTab === 'movie' && <MovieManagement />}
          {activeTab === 'video' && <VideoManagement />}
          {activeTab === 'profile' && <ProfileManagement />}
        </div>
      </div>
    </div>
  )
}

function MusicManagement() {
  const [musicVideos, setMusicVideos] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingVideo, setEditingVideo] = useState<any>(null)
  const [formData, setFormData] = useState({
    title: '',
    artist: '',
    description: '',
    bilibili_url: '',
    thumbnail_url: ''
  })

  useEffect(() => {
    fetchMusicVideos()
  }, [])

  const fetchMusicVideos = async () => {
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/music', {
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setMusicVideos(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch music videos:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const method = editingVideo ? 'PUT' : 'POST'
      const body = editingVideo ? { ...formData, id: editingVideo.id } : formData

      const response = await fetch('/api/admin/music', {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify(body)
      })

      if (response.ok) {
        setShowAddForm(false)
        setEditingVideo(null)
        setFormData({ title: '', artist: '', description: '', bilibili_url: '', thumbnail_url: '' })
        fetchMusicVideos()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败，请检查输入信息')
      }
    } catch (error) {
      console.error('Failed to submit:', error)
      alert('操作失败')
    }
  }

  const handleEdit = (video: any) => {
    setEditingVideo(video)
    setFormData({
      title: video.title,
      artist: video.artist,
      description: video.description || '',
      bilibili_url: video.bilibili_url,
      thumbnail_url: video.thumbnail_url || ''
    })
    setShowAddForm(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个音乐视频吗？')) return

    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch(`/api/admin/music?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })

      if (response.ok) {
        fetchMusicVideos()
      } else {
        alert('删除失败')
      }
    } catch (error) {
      console.error('Failed to delete video:', error)
      alert('删除失败')
    }
  }

  const handleCancel = () => {
    setShowAddForm(false)
    setEditingVideo(null)
    setFormData({ title: '', artist: '', description: '', bilibili_url: '', thumbnail_url: '' })
  }

  if (isLoading) {
    return <div className="text-center py-8">加载中...</div>
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">音乐视频管理</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus className="mr-2" size={20} />
          添加音乐视频
        </button>
      </div>

      {showAddForm && (
        <div className="bg-gray-700 p-6 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-4">
            {editingVideo ? '编辑音乐视频' : '添加新音乐视频'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">视频标题 *</label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">演唱者 *</label>
              <input
                type="text"
                required
                value={formData.artist}
                onChange={(e) => setFormData({...formData, artist: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">B站视频链接 *</label>
              <input
                type="url"
                required
                placeholder="https://www.bilibili.com/video/BV..."
                value={formData.bilibili_url}
                onChange={(e) => setFormData({...formData, bilibili_url: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
              <p className="text-xs text-gray-400 mt-1">
                支持格式：https://www.bilibili.com/video/BV... 或 https://b23.tv/...
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">缩略图URL</label>
              <input
                type="url"
                placeholder="/thumbnails/video.jpg"
                value={formData.thumbnail_url}
                onChange={(e) => setFormData({...formData, thumbnail_url: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">描述</label>
              <textarea
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div className="flex space-x-4">
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                {editingVideo ? '更新' : '添加'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-4">
        {musicVideos.map((video) => (
          <div key={video.id} className="bg-gray-700 p-4 rounded-lg flex items-center justify-between">
            <div className="flex-1">
              <h3 className="font-semibold">{video.title}</h3>
              <p className="text-gray-400 text-sm">演唱：{video.artist}</p>
              {video.description && (
                <p className="text-gray-500 text-sm mt-1 line-clamp-2">{video.description}</p>
              )}
              <div className="flex items-center mt-2 space-x-4">
                <a
                  href={video.bilibili_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  在B站观看 →
                </a>
                <span className="text-xs text-gray-500">
                  {new Date(video.created_at).toLocaleDateString('zh-CN')}
                </span>
              </div>
            </div>
            <div className="flex space-x-2 ml-4">
              <button
                onClick={() => handleEdit(video)}
                className="text-blue-400 hover:text-blue-300 p-2"
                title="编辑"
              >
                <Edit size={20} />
              </button>
              <button
                onClick={() => handleDelete(video.id)}
                className="text-red-400 hover:text-red-300 p-2"
                title="删除"
              >
                <Trash2 size={20} />
              </button>
            </div>
          </div>
        ))}

        {musicVideos.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            暂无音乐视频
          </div>
        )}
      </div>
    </div>
  )
}

function VideoManagement() {
  const [videos, setVideos] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    bilibili_url: ''
  })

  useEffect(() => {
    fetchVideos()
  }, [])

  const fetchVideos = async () => {
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/videos', {
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setVideos(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch videos:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddVideo = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/videos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        setShowAddForm(false)
        setFormData({ title: '', description: '', bilibili_url: '' })
        fetchVideos()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '添加失败，请检查输入信息')
      }
    } catch (error) {
      console.error('Failed to add video:', error)
      alert('添加失败')
    }
  }

  const handleDeleteVideo = async (id: string) => {
    if (!confirm('确定要删除这个视频吗？')) return

    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch(`/api/admin/videos?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })

      if (response.ok) {
        fetchVideos()
      } else {
        alert('删除失败')
      }
    } catch (error) {
      console.error('Failed to delete video:', error)
      alert('删除失败')
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">加载中...</div>
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">视频作品管理</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus className="mr-2" size={20} />
          添加视频
        </button>
      </div>

      {showAddForm && (
        <div className="bg-gray-700 p-6 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-4">添加新视频</h3>
          <form onSubmit={handleAddVideo} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">视频标题 *</label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">B站视频链接 *</label>
              <input
                type="url"
                required
                placeholder="https://www.bilibili.com/video/BV..."
                value={formData.bilibili_url}
                onChange={(e) => setFormData({...formData, bilibili_url: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
              <p className="text-gray-400 text-xs mt-1">请输入完整的B站视频链接，包含BV号</p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">视频描述</label>
              <textarea
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div className="flex space-x-4">
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                添加
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-4">
        {videos.map((video) => (
          <div key={video.id} className="bg-gray-700 p-4 rounded-lg flex items-center justify-between">
            <div className="flex-grow">
              <h3 className="font-semibold">{video.title}</h3>
              <p className="text-gray-400 text-sm">发布时间：{video.created_at}</p>
              <p className="text-gray-400 text-sm">BV号：{video.bilibili_embed_id}</p>
              {video.description && (
                <p className="text-gray-500 text-sm mt-1">{video.description}</p>
              )}
              <a
                href={video.bilibili_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 text-sm"
              >
                在B站观看 →
              </a>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleDeleteVideo(video.id)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <Trash2 size={20} />
              </button>
            </div>
          </div>
        ))}

        {videos.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            暂无视频作品
          </div>
        )}
      </div>
    </div>
  )
}

function MovieManagement() {
  const [movies, setMovies] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingMovie, setEditingMovie] = useState<any>(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    poster_url: '',
    movie_link: '',
    category: 'drama',
    rating: '',
    release_year: ''
  })

  const categories = [
    { id: 'gangster', label: '黑帮类' },
    { id: 'romance', label: '情感类' },
    { id: 'history', label: '历史类' },
    { id: 'thriller', label: '惊悚类' },
    { id: 'drama', label: '剧情类' },
    { id: 'action', label: '动作类' },
  ]

  useEffect(() => {
    fetchMovies()
  }, [])

  const fetchMovies = async () => {
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/movies', {
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setMovies(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch movies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const method = editingMovie ? 'PUT' : 'POST'

      // 处理数字字段
      const submitData = {
        ...formData,
        rating: formData.rating ? parseFloat(formData.rating) : null,
        release_year: formData.release_year ? parseInt(formData.release_year) : null,
        ...(editingMovie && { id: editingMovie.id })
      }

      const response = await fetch('/api/admin/movies', {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify(submitData)
      })

      if (response.ok) {
        setShowAddForm(false)
        setEditingMovie(null)
        setFormData({
          title: '',
          description: '',
          poster_url: '',
          movie_link: '',
          category: 'drama',
          rating: '',
          release_year: ''
        })
        fetchMovies()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败，请检查输入信息')
      }
    } catch (error) {
      console.error('Failed to submit:', error)
      alert('操作失败')
    }
  }

  const handleEdit = (movie: any) => {
    setEditingMovie(movie)
    setFormData({
      title: movie.title,
      description: movie.description,
      poster_url: movie.poster_url || '',
      movie_link: movie.movie_link || '',
      category: movie.category,
      rating: movie.rating ? movie.rating.toString() : '',
      release_year: movie.release_year ? movie.release_year.toString() : ''
    })
    setShowAddForm(true)
  }

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这部电影吗？')) return

    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch(`/api/admin/movies?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })

      if (response.ok) {
        fetchMovies()
      } else {
        alert('删除失败')
      }
    } catch (error) {
      console.error('Failed to delete movie:', error)
      alert('删除失败')
    }
  }

  const handleCancel = () => {
    setShowAddForm(false)
    setEditingMovie(null)
    setFormData({
      title: '',
      description: '',
      poster_url: '',
      movie_link: '',
      category: 'drama',
      rating: '',
      release_year: ''
    })
  }

  if (isLoading) {
    return <div className="text-center py-8">加载中...</div>
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">电影管理</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus className="mr-2" size={20} />
          添加电影
        </button>
      </div>

      {showAddForm && (
        <div className="bg-gray-700 p-6 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-4">
            {editingMovie ? '编辑电影' : '添加新电影'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">电影名称 *</label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">分类 *</label>
                <select
                  required
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                  className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                >
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.id}>{cat.label}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">电影描述 *</label>
              <textarea
                rows={4}
                required
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">海报图片URL</label>
                <input
                  type="url"
                  placeholder="/posters/movie.jpg"
                  value={formData.poster_url}
                  onChange={(e) => setFormData({...formData, poster_url: e.target.value})}
                  className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">观看链接</label>
                <input
                  type="url"
                  placeholder="https://example.com/movie"
                  value={formData.movie_link}
                  onChange={(e) => setFormData({...formData, movie_link: e.target.value})}
                  className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">评分 (0-10)</label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  step="0.1"
                  placeholder="8.5"
                  value={formData.rating}
                  onChange={(e) => setFormData({...formData, rating: e.target.value})}
                  className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">上映年份</label>
                <input
                  type="number"
                  min="1900"
                  max={new Date().getFullYear()}
                  placeholder="2023"
                  value={formData.release_year}
                  onChange={(e) => setFormData({...formData, release_year: e.target.value})}
                  className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                />
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                {editingMovie ? '更新' : '添加'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="grid gap-4">
        {movies.map((movie) => (
          <div key={movie.id} className="bg-gray-700 p-4 rounded-lg flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {movie.poster_url ? (
                    <img
                      src={movie.poster_url}
                      alt={movie.title}
                      className="w-16 h-24 object-cover rounded"
                    />
                  ) : (
                    <div className="w-16 h-24 bg-gray-600 rounded flex items-center justify-center">
                      <Film className="text-gray-400" size={24} />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">{movie.title}</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-400 mb-2">
                    <span className="px-2 py-1 bg-gray-600 rounded text-xs">
                      {categories.find(cat => cat.id === movie.category)?.label}
                    </span>
                    {movie.rating && (
                      <span className="flex items-center">
                        ⭐ {movie.rating}
                      </span>
                    )}
                    {movie.release_year && <span>{movie.release_year}</span>}
                  </div>
                  <p className="text-gray-300 text-sm line-clamp-3 mb-2">{movie.description}</p>
                  <div className="flex items-center space-x-4">
                    {movie.movie_link && (
                      <a
                        href={movie.movie_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 text-sm"
                      >
                        观看电影 →
                      </a>
                    )}
                    <span className="text-xs text-gray-500">
                      {new Date(movie.created_at).toLocaleDateString('zh-CN')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-2 ml-4">
              <button
                onClick={() => handleEdit(movie)}
                className="text-blue-400 hover:text-blue-300 p-2"
                title="编辑"
              >
                <Edit size={20} />
              </button>
              <button
                onClick={() => handleDelete(movie.id)}
                className="text-red-400 hover:text-red-300 p-2"
                title="删除"
              >
                <Trash2 size={20} />
              </button>
            </div>
          </div>
        ))}

        {movies.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            暂无电影信息
          </div>
        )}
      </div>
    </div>
  )
}

function ProfileManagement() {
  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">个人信息管理</h2>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">姓名</label>
          <input
            type="text"
            defaultValue="马君"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">个人简介</label>
          <textarea
            rows={4}
            defaultValue="音乐创作者 · 视频制作人"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
          />
        </div>
        
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
          保存更改
        </button>
      </div>
    </div>
  )
}
