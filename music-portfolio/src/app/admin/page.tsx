'use client'

import { useState, useEffect } from 'react'
import { Settings, Music, Video, User, Plus, Edit, Trash2 } from 'lucide-react'

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('music')
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查认证状态
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/admin/auth', {
        credentials: 'include'
      })
      setIsAuthenticated(response.ok)
    } catch (error) {
      setIsAuthenticated(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogin = () => {
    const username = prompt('请输入用户名:')
    const password = prompt('请输入密码:')

    if (username && password) {
      const credentials = btoa(`${username}:${password}`)
      fetch('/api/admin/auth', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      }).then(response => {
        if (response.ok) {
          // 保存认证信息到localStorage
          localStorage.setItem('admin_username', username)
          localStorage.setItem('admin_password', password)
          setIsAuthenticated(true)
        } else {
          alert('认证失败，请检查用户名和密码')
        }
      })
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-400">加载中...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg max-w-md w-full mx-4">
          <div className="text-center mb-8">
            <Settings className="mx-auto text-blue-400 mb-4" size={48} />
            <h1 className="text-2xl font-bold mb-2">后台管理系统</h1>
            <p className="text-gray-400">请登录以访问管理功能</p>
          </div>
          
          <button
            onClick={handleLogin}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
          >
            登录
          </button>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'music', label: '音乐管理', icon: Music },
    { id: 'video', label: '视频管理', icon: Video },
    { id: 'profile', label: '个人信息', icon: User },
  ]

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">后台管理系统</h1>
          <p className="text-gray-400">管理您的音乐和视频内容</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-700 mb-8">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="mr-2" size={20} />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="bg-gray-800 rounded-lg p-6">
          {activeTab === 'music' && <MusicManagement />}
          {activeTab === 'video' && <VideoManagement />}
          {activeTab === 'profile' && <ProfileManagement />}
        </div>
      </div>
    </div>
  )
}

function MusicManagement() {
  const [musicTracks, setMusicTracks] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    artist: '',
    duration: '',
    audio_url: '',
    description: ''
  })

  useEffect(() => {
    fetchMusicTracks()
  }, [])

  const fetchMusicTracks = async () => {
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/music', {
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setMusicTracks(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch music tracks:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddMusic = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/music', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        setShowAddForm(false)
        setFormData({ title: '', artist: '', duration: '', audio_url: '', description: '' })
        fetchMusicTracks()
      } else {
        alert('添加失败，请检查输入信息')
      }
    } catch (error) {
      console.error('Failed to add music:', error)
      alert('添加失败')
    }
  }

  const handleDeleteMusic = async (id: string) => {
    if (!confirm('确定要删除这首音乐吗？')) return

    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch(`/api/admin/music?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })

      if (response.ok) {
        fetchMusicTracks()
      } else {
        alert('删除失败')
      }
    } catch (error) {
      console.error('Failed to delete music:', error)
      alert('删除失败')
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">加载中...</div>
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">音乐作品管理</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus className="mr-2" size={20} />
          添加音乐
        </button>
      </div>

      {showAddForm && (
        <div className="bg-gray-700 p-6 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-4">添加新音乐</h3>
          <form onSubmit={handleAddMusic} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">歌曲标题 *</label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">演唱者 *</label>
              <input
                type="text"
                required
                value={formData.artist}
                onChange={(e) => setFormData({...formData, artist: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">时长</label>
              <input
                type="text"
                placeholder="例如: 3:45"
                value={formData.duration}
                onChange={(e) => setFormData({...formData, duration: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">音频文件URL *</label>
              <input
                type="url"
                required
                placeholder="/music/song.mp3"
                value={formData.audio_url}
                onChange={(e) => setFormData({...formData, audio_url: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">描述</label>
              <textarea
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div className="flex space-x-4">
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                添加
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-4">
        {musicTracks.map((track) => (
          <div key={track.id} className="bg-gray-700 p-4 rounded-lg flex items-center justify-between">
            <div>
              <h3 className="font-semibold">{track.title}</h3>
              <p className="text-gray-400 text-sm">演唱：{track.artist} | 时长：{track.duration}</p>
              {track.description && (
                <p className="text-gray-500 text-sm mt-1">{track.description}</p>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleDeleteMusic(track.id)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <Trash2 size={20} />
              </button>
            </div>
          </div>
        ))}

        {musicTracks.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            暂无音乐作品
          </div>
        )}
      </div>
    </div>
  )
}

function VideoManagement() {
  const [videos, setVideos] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    bilibili_url: ''
  })

  useEffect(() => {
    fetchVideos()
  }, [])

  const fetchVideos = async () => {
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/videos', {
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setVideos(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch videos:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddVideo = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch('/api/admin/videos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        setShowAddForm(false)
        setFormData({ title: '', description: '', bilibili_url: '' })
        fetchVideos()
      } else {
        const errorData = await response.json()
        alert(errorData.error || '添加失败，请检查输入信息')
      }
    } catch (error) {
      console.error('Failed to add video:', error)
      alert('添加失败')
    }
  }

  const handleDeleteVideo = async (id: string) => {
    if (!confirm('确定要删除这个视频吗？')) return

    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)
      const response = await fetch(`/api/admin/videos?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Basic ${credentials}`
        }
      })

      if (response.ok) {
        fetchVideos()
      } else {
        alert('删除失败')
      }
    } catch (error) {
      console.error('Failed to delete video:', error)
      alert('删除失败')
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">加载中...</div>
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">视频作品管理</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <Plus className="mr-2" size={20} />
          添加视频
        </button>
      </div>

      {showAddForm && (
        <div className="bg-gray-700 p-6 rounded-lg mb-6">
          <h3 className="text-lg font-semibold mb-4">添加新视频</h3>
          <form onSubmit={handleAddVideo} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">视频标题 *</label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">B站视频链接 *</label>
              <input
                type="url"
                required
                placeholder="https://www.bilibili.com/video/BV..."
                value={formData.bilibili_url}
                onChange={(e) => setFormData({...formData, bilibili_url: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
              <p className="text-gray-400 text-xs mt-1">请输入完整的B站视频链接，包含BV号</p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">视频描述</label>
              <textarea
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              />
            </div>
            <div className="flex space-x-4">
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                添加
              </button>
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-4">
        {videos.map((video) => (
          <div key={video.id} className="bg-gray-700 p-4 rounded-lg flex items-center justify-between">
            <div className="flex-grow">
              <h3 className="font-semibold">{video.title}</h3>
              <p className="text-gray-400 text-sm">发布时间：{video.created_at}</p>
              <p className="text-gray-400 text-sm">BV号：{video.bilibili_embed_id}</p>
              {video.description && (
                <p className="text-gray-500 text-sm mt-1">{video.description}</p>
              )}
              <a
                href={video.bilibili_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 text-sm"
              >
                在B站观看 →
              </a>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleDeleteVideo(video.id)}
                className="text-red-400 hover:text-red-300 p-2"
              >
                <Trash2 size={20} />
              </button>
            </div>
          </div>
        ))}

        {videos.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            暂无视频作品
          </div>
        )}
      </div>
    </div>
  )
}

function ProfileManagement() {
  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">个人信息管理</h2>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">姓名</label>
          <input
            type="text"
            defaultValue="马君"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">个人简介</label>
          <textarea
            rows={4}
            defaultValue="音乐创作者 · 视频制作人"
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
          />
        </div>
        
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
          保存更改
        </button>
      </div>
    </div>
  )
}
