import { NextResponse } from 'next/server'

// 这里应该从数据库获取数据，现在使用模拟数据
const musicTracks = [
  {
    id: '1',
    title: '夜空中最亮的星',
    artist: 'KIMAHALA',
    duration: '4:32',
    audio_url: '/music/sample1.mp3',
    cover_image: '/covers/cover1.jpg',
    description: '一首关于梦想和希望的歌曲',
    created_at: '2024-01-15',
  },
  {
    id: '2',
    title: '时光倒流',
    artist: 'KIMAHALA',
    duration: '3:45',
    audio_url: '/music/sample2.mp3',
    cover_image: '/covers/cover2.jpg',
    description: '回忆过往美好时光的抒情歌曲',
    created_at: '2024-01-10',
  },
  {
    id: '3',
    title: '远方的路',
    artist: 'KIMAHALA',
    duration: '5:18',
    audio_url: '/music/sample3.mp3',
    cover_image: '/covers/cover3.jpg',
    description: '关于追寻远方和自由的摇滚歌曲',
    created_at: '2024-01-05',
  },
]

export async function GET() {
  return NextResponse.json({ success: true, data: musicTracks })
}
