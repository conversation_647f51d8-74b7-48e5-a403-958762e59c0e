import { NextResponse } from 'next/server'

// 这里应该从数据库获取数据，现在使用模拟数据
const musicVideos = [
  {
    id: '1',
    title: '《夜空中最亮的星》原创音乐MV',
    artist: 'KIMAHALA',
    description: '我的第一首原创歌曲，献给所有在黑暗中寻找光明的人。这首歌记录了我对音乐的初心和对梦想的坚持。',
    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',
    bilibili_embed_id: 'BV1234567890',
    thumbnail_url: '/thumbnails/music1.jpg',
    created_at: '2024-01-15',
  },
  {
    id: '2',
    title: '《时光倒流》吉他弹唱版',
    artist: 'KIMAHALA',
    description: '用最简单的吉他和歌声，诉说关于时光和回忆的故事。希望这首歌能唤起你心中最美好的回忆。',
    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',
    bilibili_embed_id: 'BV2345678901',
    thumbnail_url: '/thumbnails/music2.jpg',
    created_at: '2024-01-12',
  },
  {
    id: '3',
    title: '《远方的路》创作过程分享',
    artist: 'KIMAHALA',
    description: '分享这首摇滚歌曲的创作过程，从灵感迸发到最终完成的全过程记录。包含编曲、录音等幕后花絮。',
    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',
    bilibili_embed_id: 'BV3456789012',
    thumbnail_url: '/thumbnails/music3.jpg',
    created_at: '2024-01-10',
  },
  {
    id: '4',
    title: '《青春无悔》现场演出版',
    artist: 'KIMAHALA',
    description: '在小型音乐节上的现场演出，感受最真实的音乐力量。这是我和观众最直接的音乐交流。',
    bilibili_url: 'https://www.bilibili.com/video/BV4567890123',
    bilibili_embed_id: 'BV4567890123',
    thumbnail_url: '/thumbnails/music4.jpg',
    created_at: '2024-01-08',
  },
  {
    id: '5',
    title: '翻唱《平凡之路》',
    artist: 'KIMAHALA',
    description: '用我的方式重新诠释朴树的经典作品《平凡之路》，加入了一些个人的理解和情感表达。',
    bilibili_url: 'https://www.bilibili.com/video/BV5678901234',
    bilibili_embed_id: 'BV5678901234',
    thumbnail_url: '/thumbnails/music5.jpg',
    created_at: '2024-01-05',
  },
]

export async function GET() {
  return NextResponse.json({ success: true, data: musicVideos })
}
