import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'

// 从B站URL中提取视频ID
function extractBilibiliId(url: string): string | null {
  try {
    // 支持多种B站URL格式
    const patterns = [
      /bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/,
      /bilibili\.com\/video\/(av\d+)/,
      /b23\.tv\/([a-zA-Z0-9]+)/,
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return null
  } catch (error) {
    return null
  }
}

// 模拟数据存储 - 实际项目中应该使用数据库
let musicVideos = [
  {
    id: '1',
    title: '《夜空中最亮的星》原创音乐MV',
    artist: 'KIMAHALA',
    description: '我的第一首原创歌曲，献给所有在黑暗中寻找光明的人。这首歌记录了我对音乐的初心和对梦想的坚持。',
    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',
    bilibili_embed_id: 'BV1234567890',
    thumbnail_url: '/thumbnails/music1.jpg',
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: '2',
    title: '《时光倒流》吉他弹唱版',
    artist: 'KIMAHALA',
    description: '用最简单的吉他和歌声，诉说关于时光和回忆的故事。希望这首歌能唤起你心中最美好的回忆。',
    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',
    bilibili_embed_id: 'BV2345678901',
    thumbnail_url: '/thumbnails/music2.jpg',
    created_at: '2024-01-12T00:00:00Z',
    updated_at: '2024-01-12T00:00:00Z',
  },
  {
    id: '3',
    title: '《远方的路》创作过程分享',
    artist: 'KIMAHALA',
    description: '分享这首摇滚歌曲的创作过程，从灵感迸发到最终完成的全过程记录。包含编曲、录音等幕后花絮。',
    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',
    bilibili_embed_id: 'BV3456789012',
    thumbnail_url: '/thumbnails/music3.jpg',
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z',
  },
]

export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  return NextResponse.json({ success: true, data: musicVideos })
}

export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { title, artist, description, bilibili_url, thumbnail_url } = body

    if (!title || !artist || !bilibili_url) {
      return NextResponse.json(
        { error: 'Title, artist, and bilibili_url are required' },
        { status: 400 }
      )
    }

    // 从B站URL中提取embed ID
    const bilibiliEmbedId = extractBilibiliId(bilibili_url)
    if (!bilibiliEmbedId) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL' },
        { status: 400 }
      )
    }

    const newVideo = {
      id: Date.now().toString(),
      title,
      artist,
      description: description || '',
      bilibili_url,
      bilibili_embed_id: bilibiliEmbedId,
      thumbnail_url: thumbnail_url || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    musicVideos.push(newVideo)

    return NextResponse.json({ success: true, data: newVideo })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

// 更新音乐视频
export async function PUT(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { id, title, artist, description, bilibili_url, thumbnail_url } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      )
    }

    const videoIndex = musicVideos.findIndex(video => video.id === id)
    if (videoIndex === -1) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    if (!title || !artist || !bilibili_url) {
      return NextResponse.json(
        { error: 'Title, artist, and bilibili_url are required' },
        { status: 400 }
      )
    }

    // 从B站URL中提取embed ID
    const bilibiliEmbedId = extractBilibiliId(bilibili_url)
    if (!bilibiliEmbedId) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL' },
        { status: 400 }
      )
    }

    const updatedVideo = {
      ...musicVideos[videoIndex],
      title,
      artist,
      description: description || '',
      bilibili_url,
      bilibili_embed_id: bilibiliEmbedId,
      thumbnail_url: thumbnail_url || '',
      updated_at: new Date().toISOString(),
    }

    musicVideos[videoIndex] = updatedVideo

    return NextResponse.json({ success: true, data: updatedVideo })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const index = musicVideos.findIndex(video => video.id === id)

    if (index === -1) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    musicVideos.splice(index, 1)

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete track' },
      { status: 500 }
    )
  }
}
