import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'

// 模拟数据存储 - 实际项目中应该使用数据库
let musicTracks = [
  {
    id: '1',
    title: '夜空中最亮的星',
    artist: 'KIMAHALA',
    duration: '4:32',
    audio_url: '/music/sample1.mp3',
    cover_image: '/covers/cover1.jpg',
    description: '一首关于梦想和希望的歌曲',
    created_at: '2024-01-15',
    updated_at: '2024-01-15',
  },
  {
    id: '2',
    title: '时光倒流',
    artist: 'KIMAHA<PERSON>',
    duration: '3:45',
    audio_url: '/music/sample2.mp3',
    cover_image: '/covers/cover2.jpg',
    description: '回忆过往美好时光的抒情歌曲',
    created_at: '2024-01-10',
    updated_at: '2024-01-10',
  },
  {
    id: '3',
    title: '远方的路',
    artist: 'KIMAHALA',
    duration: '5:18',
    audio_url: '/music/sample3.mp3',
    cover_image: '/covers/cover3.jpg',
    description: '关于追寻远方和自由的摇滚歌曲',
    created_at: '2024-01-05',
    updated_at: '2024-01-05',
  },
]

export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  return NextResponse.json({ success: true, data: musicTracks })
}

export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { title, artist, duration, audio_url, cover_image, description } = body

    if (!title || !artist || !audio_url) {
      return NextResponse.json(
        { error: 'Title, artist, and audio_url are required' },
        { status: 400 }
      )
    }

    const newTrack = {
      id: Date.now().toString(),
      title,
      artist,
      duration: duration || '0:00',
      audio_url,
      cover_image: cover_image || '',
      description: description || '',
      created_at: new Date().toISOString().split('T')[0],
      updated_at: new Date().toISOString().split('T')[0],
    }

    musicTracks.push(newTrack)

    return NextResponse.json({ success: true, data: newTrack })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const index = musicTracks.findIndex(track => track.id === id)
    
    if (index === -1) {
      return NextResponse.json(
        { error: 'Track not found' },
        { status: 404 }
      )
    }

    musicTracks.splice(index, 1)

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete track' },
      { status: 500 }
    )
  }
}
