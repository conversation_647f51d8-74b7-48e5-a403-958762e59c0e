import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'
import {
  getMusicVideos,
  addMusicVideo,
  updateMusicVideo,
  deleteMusicVideo
} from '@/lib/data-store'

// 从B站URL中提取视频ID
function extractBilibiliId(url: string): string | null {
  try {
    // 支持多种B站URL格式
    const patterns = [
      /bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/,
      /bilibili\.com\/video\/(av\d+)/,
      /b23\.tv\/([a-zA-Z0-9]+)/,
    ]

    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return null
  } catch (error) {
    return null
  }
}

export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  const musicVideos = getMusicVideos()
  return NextResponse.json({ success: true, data: musicVideos })
}

export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { title, artist, description, bilibili_url, thumbnail_url } = body

    if (!title || !artist || !bilibili_url) {
      return NextResponse.json(
        { error: 'Title, artist, and bilibili_url are required' },
        { status: 400 }
      )
    }

    // 从B站URL中提取embed ID
    const bilibiliEmbedId = extractBilibiliId(bilibili_url)
    if (!bilibiliEmbedId) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL' },
        { status: 400 }
      )
    }

    const newVideo = {
      id: Date.now().toString(),
      title,
      artist,
      description: description || '',
      bilibili_url,
      bilibili_embed_id: bilibiliEmbedId,
      thumbnail_url: thumbnail_url || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    addMusicVideo(newVideo)

    return NextResponse.json({ success: true, data: newVideo })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

// 更新音乐视频
export async function PUT(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { id, title, artist, description, bilibili_url, thumbnail_url } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      )
    }

    const musicVideos = getMusicVideos()
    const video = musicVideos.find(video => video.id === id)
    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    if (!title || !artist || !bilibili_url) {
      return NextResponse.json(
        { error: 'Title, artist, and bilibili_url are required' },
        { status: 400 }
      )
    }

    // 从B站URL中提取embed ID
    const bilibiliEmbedId = extractBilibiliId(bilibili_url)
    if (!bilibiliEmbedId) {
      return NextResponse.json(
        { error: 'Invalid Bilibili URL' },
        { status: 400 }
      )
    }

    const updatedVideo = {
      ...video,
      title,
      artist,
      description: description || '',
      bilibili_url,
      bilibili_embed_id: bilibiliEmbedId,
      thumbnail_url: thumbnail_url || '',
      updated_at: new Date().toISOString(),
    }

    updateMusicVideo(id, updatedVideo)

    return NextResponse.json({ success: true, data: updatedVideo })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }

    const musicVideos = getMusicVideos()
    const video = musicVideos.find(video => video.id === id)

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    deleteMusicVideo(id)

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete track' },
      { status: 500 }
    )
  }
}
