import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'
import {
  getMovies,
  addMovie,
  updateMovie,
  deleteMovie
} from '@/lib/data-store'

// 获取所有电影
export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)

  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  const movies = getMovies()
  return NextResponse.json({
    success: true,
    data: movies
  })
}

// 创建新电影
export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { title, description, poster_url, movie_link, category, rating, release_year } = body

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: 'Title, description, and category are required' },
        { status: 400 }
      )
    }

    // 验证分类
    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    // 验证评分
    if (rating && (rating < 0 || rating > 10)) {
      return NextResponse.json(
        { error: 'Rating must be between 0 and 10' },
        { status: 400 }
      )
    }

    // 验证年份
    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {
      return NextResponse.json(
        { error: 'Invalid release year' },
        { status: 400 }
      )
    }

    const newMovie = {
      id: Date.now().toString(),
      title,
      description,
      poster_url: poster_url || null,
      movie_link: movie_link || null,
      category,
      rating: rating || null,
      release_year: release_year || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    addMovie(newMovie)

    return NextResponse.json({
      success: true,
      data: newMovie
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

// 更新电影
export async function PUT(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { id, title, description, poster_url, movie_link, category, rating, release_year } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Movie ID is required' },
        { status: 400 }
      )
    }

    const movies = getMovies()
    const movie = movies.find(movie => movie.id === id)
    if (!movie) {
      return NextResponse.json(
        { error: 'Movie not found' },
        { status: 404 }
      )
    }

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: 'Title, description, and category are required' },
        { status: 400 }
      )
    }

    // 验证分类
    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    // 验证评分
    if (rating && (rating < 0 || rating > 10)) {
      return NextResponse.json(
        { error: 'Rating must be between 0 and 10' },
        { status: 400 }
      )
    }

    // 验证年份
    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {
      return NextResponse.json(
        { error: 'Invalid release year' },
        { status: 400 }
      )
    }

    const updatedMovie = {
      ...movie,
      title,
      description,
      poster_url: poster_url || null,
      movie_link: movie_link || null,
      category,
      rating: rating || null,
      release_year: release_year || null,
      updated_at: new Date().toISOString(),
    }

    updateMovie(id, updatedMovie)

    return NextResponse.json({
      success: true,
      data: updatedMovie
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

// 删除电影
export async function DELETE(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Movie ID is required' },
        { status: 400 }
      )
    }

    const movies = getMovies()
    const movie = movies.find(movie => movie.id === id)
    if (!movie) {
      return NextResponse.json(
        { error: 'Movie not found' },
        { status: 404 }
      )
    }

    deleteMovie(id)

    return NextResponse.json({
      success: true,
      data: movie
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete movie' },
      { status: 500 }
    )
  }
}
