import { NextRequest, NextResponse } from 'next/server'
import { checkAdminAuth, requireAuth } from '@/lib/auth'

// 模拟数据存储 - 实际应用中应该使用数据库
let movies = [
  {
    id: '1',
    title: '教父',
    description: '一个黑手党家族的传奇故事，展现了权力、家族和忠诚的复杂关系。弗朗西斯·福特·科波拉执导的经典之作，被誉为电影史上最伟大的作品之一。',
    poster_url: '/posters/godfather.jpg',
    movie_link: 'https://example.com/godfather',
    category: 'gangster',
    rating: 9.3,
    release_year: 1972,
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  },
  {
    id: '2',
    title: '肖申克的救赎',
    description: '一个关于希望、友谊和救赎的感人故事。安迪在监狱中的经历告诉我们，即使在最黑暗的地方，希望之光也永远不会熄灭。',
    poster_url: '/posters/shawshank.jpg',
    movie_link: 'https://example.com/shawshank',
    category: 'drama',
    rating: 9.7,
    release_year: 1994,
    created_at: '2024-01-14T00:00:00Z',
    updated_at: '2024-01-14T00:00:00Z',
  },
]

// 获取所有电影
export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  return NextResponse.json({ 
    success: true, 
    data: movies 
  })
}

// 创建新电影
export async function POST(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { title, description, poster_url, movie_link, category, rating, release_year } = body

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: 'Title, description, and category are required' },
        { status: 400 }
      )
    }

    // 验证分类
    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    // 验证评分
    if (rating && (rating < 0 || rating > 10)) {
      return NextResponse.json(
        { error: 'Rating must be between 0 and 10' },
        { status: 400 }
      )
    }

    // 验证年份
    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {
      return NextResponse.json(
        { error: 'Invalid release year' },
        { status: 400 }
      )
    }

    const newMovie = {
      id: Date.now().toString(),
      title,
      description,
      poster_url: poster_url || null,
      movie_link: movie_link || null,
      category,
      rating: rating || null,
      release_year: release_year || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    movies.push(newMovie)

    return NextResponse.json({ 
      success: true, 
      data: newMovie 
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

// 更新电影
export async function PUT(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const body = await request.json()
    const { id, title, description, poster_url, movie_link, category, rating, release_year } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Movie ID is required' },
        { status: 400 }
      )
    }

    const movieIndex = movies.findIndex(movie => movie.id === id)
    if (movieIndex === -1) {
      return NextResponse.json(
        { error: 'Movie not found' },
        { status: 404 }
      )
    }

    // 验证必填字段
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: 'Title, description, and category are required' },
        { status: 400 }
      )
    }

    // 验证分类
    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }

    // 验证评分
    if (rating && (rating < 0 || rating > 10)) {
      return NextResponse.json(
        { error: 'Rating must be between 0 and 10' },
        { status: 400 }
      )
    }

    // 验证年份
    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {
      return NextResponse.json(
        { error: 'Invalid release year' },
        { status: 400 }
      )
    }

    const updatedMovie = {
      ...movies[movieIndex],
      title,
      description,
      poster_url: poster_url || null,
      movie_link: movie_link || null,
      category,
      rating: rating || null,
      release_year: release_year || null,
      updated_at: new Date().toISOString(),
    }

    movies[movieIndex] = updatedMovie

    return NextResponse.json({ 
      success: true, 
      data: updatedMovie 
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    )
  }
}

// 删除电影
export async function DELETE(request: NextRequest) {
  const auth = checkAdminAuth(request)
  
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Movie ID is required' },
        { status: 400 }
      )
    }

    const movieIndex = movies.findIndex(movie => movie.id === id)
    if (movieIndex === -1) {
      return NextResponse.json(
        { error: 'Movie not found' },
        { status: 404 }
      )
    }

    const deletedMovie = movies[movieIndex]
    movies.splice(movieIndex, 1)

    return NextResponse.json({ 
      success: true, 
      data: deletedMovie 
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to delete movie' },
      { status: 500 }
    )
  }
}
