{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/music/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\n// 这里应该从数据库获取数据，现在使用模拟数据\nconst musicTracks = [\n  {\n    id: '1',\n    title: '夜空中最亮的星',\n    artist: 'KIMAHALA',\n    duration: '4:32',\n    audio_url: '/music/sample1.mp3',\n    cover_image: '/covers/cover1.jpg',\n    description: '一首关于梦想和希望的歌曲',\n    created_at: '2024-01-15',\n  },\n  {\n    id: '2',\n    title: '时光倒流',\n    artist: 'KIMAHALA',\n    duration: '3:45',\n    audio_url: '/music/sample2.mp3',\n    cover_image: '/covers/cover2.jpg',\n    description: '回忆过往美好时光的抒情歌曲',\n    created_at: '2024-01-10',\n  },\n  {\n    id: '3',\n    title: '远方的路',\n    artist: 'KIMAHALA',\n    duration: '5:18',\n    audio_url: '/music/sample3.mp3',\n    cover_image: '/covers/cover3.jpg',\n    description: '关于追寻远方和自由的摇滚歌曲',\n    created_at: '2024-01-05',\n  },\n]\n\nexport async function GET() {\n  return NextResponse.json({ success: true, data: musicTracks })\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,wBAAwB;AACxB,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa;QACb,YAAY;IACd;CACD;AAEM,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;QAAM,MAAM;IAAY;AAC9D", "debugId": null}}]}